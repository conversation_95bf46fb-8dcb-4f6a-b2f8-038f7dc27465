#!/usr/bin/env python3
"""
Unified Devo API Client module for interacting with the Devo API using the official Devo SDK.
Provides functionality to query tables using the Devo Search API.

This client is used by the backup manager to retrieve data from Devo tables.
"""

import os
import re
import time
import logging
from typing import List, Dict, Any, Optional

# Import the Devo SDK
try:
    from devo.api import Client, ClientConfig, SIMPLECOMPACT_TO_OBJ
except ImportError:
    raise ImportError("Devo SDK not found. Please install it with 'pip install devo-sdk'")

# Import dotenv for environment variables
from dotenv import load_dotenv

# Import improved thread management
import threading
import gc
from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeoutError

try:
    from .thread_manager import get_thread_manager, managed_thread_pool, log_thread_metrics
except ImportError:
    # Fallback if thread manager not available
    from contextlib import contextmanager

    @contextmanager
    def managed_thread_pool(name, max_workers=None):
        with ThreadPoolExecutor(max_workers=max_workers or 4) as pool:
            yield pool

    def log_thread_metrics():
        pass

    def get_thread_manager():
        return None

# Use standard Python exceptions
class ConfigError(Exception):
    """Configuration error."""
    pass

class ApiError(Exception):
    """API error."""
    pass

# Configure logging
logger = logging.getLogger(__name__)

# Default values
DEFAULT_TIMEOUT = 1800  # 30 minutes

class DevoClient:
    """
    Client for interacting with the Devo API using the official Devo SDK.
    """

    def __init__(self, key: Optional[str] = None, secret: Optional[str] = None, endpoint: Optional[str] = None):
        """
        Initialize the Devo API client.

        Args:
            key: Devo API key (default: from environment)
            secret: Devo API secret (default: from environment)
            endpoint: Devo query endpoint (default: from environment)

        Raises:
            ConfigError: If credentials are missing or invalid
            ApiError: If client initialization fails
        """
        # Load environment variables
        load_dotenv()

        # SECURITY FIX: Don't store credentials as instance variables
        # Store only non-sensitive configuration and validate credentials exist
        credentials = self._get_credentials(key, secret, endpoint)

        # Validate credentials without storing them
        if not credentials['key'] or not credentials['secret']:
            raise ConfigError("Devo API credentials not found",
                             {"missing": "API key and/or secret",
                              "env_vars": "DEVO_API_KEY, DEVO_API_SECRET"})

        if not credentials['endpoint']:
            raise ConfigError("Devo query endpoint not found",
                             {"missing": "API endpoint",
                              "env_var": "DEVO_QUERY_ENDPOINT"})

        # Store only the endpoint (non-sensitive)
        self.endpoint = credentials['endpoint']

        # Initialize the default client configuration
        self.default_config = ClientConfig(
            response="json/simple/compact",
            processor=SIMPLECOMPACT_TO_OBJ
        )

        # Initialize the Devo API client
        try:
            self.client = self._create_client()
            logger.debug(f"Using Devo API endpoint: {self.endpoint}")
            logger.info("Devo API client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Devo API client: {str(e)}")
            raise ApiError(f"Failed to initialize Devo API client: {str(e)}")

    def _get_credentials(self, key: Optional[str] = None, secret: Optional[str] = None, endpoint: Optional[str] = None) -> Dict[str, Optional[str]]:
        """
        Get Devo API credentials on-demand to minimize memory exposure.

        SECURITY FIX: Retrieve credentials only when needed instead of storing them.

        Args:
            key: API key (optional, will use environment if not provided)
            secret: API secret (optional, will use environment if not provided)
            endpoint: API endpoint (optional, will use environment if not provided)

        Returns:
            Dictionary with credentials
        """
        # Load environment variables
        load_dotenv()

        return {
            'key': key or os.getenv('DEVO_API_KEY'),
            'secret': secret or os.getenv('DEVO_API_SECRET'),
            'endpoint': endpoint or os.getenv('DEVO_QUERY_ENDPOINT')
        }

    def _create_client(self, custom_config=None):
        """
        Create a Devo API client with the specified configuration.

        Args:
            custom_config: Custom client configuration (default: None, uses default_config)

        Returns:
            Configured Devo API client
        """
        # SECURITY FIX: Get credentials on-demand
        credentials = self._get_credentials()
        config = custom_config or self.default_config
        return Client(
            auth={"key": credentials['key'], "secret": credentials['secret']},
            address=self.endpoint,
            config=config
        )

    def _fix_query_syntax(self, query: str) -> str:
        """
        Fix the query syntax for the Devo API.

        The Devo API has specific requirements for query syntax:
        - The where clause should not be included in the query string
        - Date filtering is handled by the dates parameter

        This method removes any where clauses related to eventdate from the query.

        Args:
            query: The original query string

        Returns:
            Fixed query string without eventdate where clauses
        """
        # Check if the query contains a where clause
        if " where " not in query.lower():
            return query

        # Split the query into parts
        parts = query.split(" where ", 1)
        base_query = parts[0]
        where_clause = parts[1] if len(parts) > 1 else ""

        # Check if the where clause is only for eventdate filtering
        if "eventdate" in where_clause and (
            "and" not in where_clause.lower() or
            all(cond.strip().lower().startswith("eventdate") for cond in where_clause.split(" and "))
        ):
            # If it's only eventdate conditions, remove the entire where clause
            logger.debug(f"Removed eventdate where clause: {where_clause}")
            return base_query
        else:
            # If there are other conditions, keep them but remove eventdate conditions
            conditions = where_clause.split(" and ")
            filtered_conditions = [c for c in conditions if not c.strip().lower().startswith("eventdate")]

            if filtered_conditions:
                # Rebuild the query with the remaining conditions
                new_where = " and ".join(filtered_conditions)
                logger.debug(f"Modified where clause: {new_where}")
                return f"{base_query} where {new_where}"
            else:
                # No conditions left after filtering
                logger.debug("Removed all conditions from where clause")
                return base_query

    def _diagnose_query_issues(self, query: str) -> None:
        """
        Diagnose potential issues in a query that might cause parsing errors.

        This method analyzes the query for common syntax issues and logs warnings
        for potential problems.

        Args:
            query: The query string to diagnose
        """
        # Check for basic syntax elements
        if "from" not in query.lower():
            logger.warning("Query missing 'from' clause")

        if "select" not in query.lower():
            logger.warning("Query missing 'select' clause")

        # Check for unbalanced quotes
        single_quotes = query.count("'")
        double_quotes = query.count('"')
        if single_quotes % 2 != 0:
            logger.warning(f"Query contains unbalanced single quotes ({single_quotes})")
        if double_quotes % 2 != 0:
            logger.warning(f"Query contains unbalanced double quotes ({double_quotes})")

        # Check for unbalanced parentheses
        open_parens = query.count('(')
        close_parens = query.count(')')
        if open_parens != close_parens:
            logger.warning(f"Query contains unbalanced parentheses (open: {open_parens}, close: {close_parens})")

        # Check for special characters in column names
        try:
            select_match = re.search(r'(?i)select\s+(.*?)\s+from', query)
            if select_match and select_match.group(1).strip() != '*':
                column_list = select_match.group(1)
                columns = [col.strip() for col in column_list.split(',')]
                for col in columns:
                    # Updated regex to better detect special characters including colons
                    if re.search(r'[^\w\s.*()\[\]_]|:{1,2}', col) and not (col.startswith('"') and col.endswith('"')):
                        logger.warning(f"Column name contains special characters and is not quoted: {col}")
                        # Provide more specific guidance for columns with colons
                        if ':' in col:
                            logger.warning(f"Column name contains colons which require double quotes: {col}")
        except Exception:
            pass

        # Check for common syntax errors
        if re.search(r'(?i)where\s+and', query):
            logger.warning("Query contains 'where and' which is invalid syntax")

        if re.search(r'(?i)where\s+or', query):
            logger.warning("Query contains 'where or' which is invalid syntax")

        if re.search(r'(?i)and\s+and', query):
            logger.warning("Query contains 'and and' which is likely a syntax error")

        if re.search(r'(?i)or\s+or', query):
            logger.warning("Query contains 'or or' which is likely a syntax error")

        # Check for potential date format issues
        date_patterns = re.findall(r'\d{4}-\d{2}-\d{2}(?:T\d{2}:\d{2}:\d{2}(?:\.\d{3})?Z?)?', query)
        for date in date_patterns:
            if not (f"'{date}'" in query or f'"{date}"' in query):
                logger.warning(f"Date value not properly quoted: {date}")

        # Check for potential SQL injection patterns (unlikely but good to check)
        if re.search(r'(?i);\s*(?:select|update|delete|insert|drop|alter)', query):
            logger.warning("Query contains potential SQL injection pattern (multiple statements)")

    def _fix_query_parsing_issues(self, query: str) -> str:
        """
        Fix common query parsing issues that might cause errors.

        This method attempts to fix various syntax issues that can cause
        "Query parsing error" responses from the Devo API.

        Args:
            query: The original query string

        Returns:
            Fixed query string with common issues resolved
        """
        # List of Devo LINQ reserved keywords that need special handling
        reserved_keywords = [
            'select', 'from', 'where', 'limit', 'offset', 'order', 'by', 'asc', 'desc',
            'group', 'having', 'join', 'left', 'right', 'inner', 'outer', 'on', 'as',
            'and', 'or', 'not', 'in', 'between', 'like', 'is', 'null', 'true', 'false',
            'case', 'when', 'then', 'else', 'end', 'distinct', 'all', 'any', 'some'
        ]

        # Make a copy of the original query
        fixed_query = query

        # Fix 1: Remove any trailing semicolons
        fixed_query = fixed_query.rstrip(';')

        # Fix 2: Fix malformed select statements
        if "select" in fixed_query.lower():
            # Ensure there's a space after 'select'
            fixed_query = re.sub(r'(?i)select(?=[^\s])', 'select ', fixed_query)

        # Fix 3: Fix malformed from statements
        if "from" in fixed_query.lower():
            # Ensure there's a space after 'from'
            fixed_query = re.sub(r'(?i)from(?=[^\s])', 'from ', fixed_query)

        # Fix 4: Fix malformed where statements
        if "where" in fixed_query.lower():
            # Ensure there's a space after 'where'
            fixed_query = re.sub(r'(?i)where(?=[^\s])', 'where ', fixed_query)

        # Fix 5: Fix malformed limit statements
        if "limit" in fixed_query.lower():
            # Ensure there's a space after 'limit'
            fixed_query = re.sub(r'(?i)limit(?=[^\s])', 'limit ', fixed_query)

        # Fix 6: Fix malformed offset statements
        if "offset" in fixed_query.lower():
            # Ensure there's a space after 'offset'
            fixed_query = re.sub(r'(?i)offset(?=[^\s])', 'offset ', fixed_query)

        # Fix 7: Remove any double spaces
        fixed_query = re.sub(r'\s+', ' ', fixed_query)

        # Fix 8: Ensure proper spacing around parentheses
        fixed_query = re.sub(r'([^\s(])\(', r'\1 (', fixed_query)
        fixed_query = re.sub(r'\)([^\s),])', r') \1', fixed_query)

        # Fix 9: Always convert to "select *" for tables with problematic column names
        # This is the most reliable approach to avoid query parsing errors
        try:
            # Extract the table name and other query components
            table_match = re.search(r'(?i)from\s+([^\s]+)', fixed_query)
            if table_match:
                table_name = table_match.group(1)

                # Extract where clause if present
                where_clause = ""
                if "where" in fixed_query.lower():
                    where_match = re.search(r'(?i)where\s+(.*?)(?:limit|offset|$)', fixed_query)
                    if where_match:
                        where_clause = f" where {where_match.group(1).strip()}"

                # Extract limit clause if present
                limit_clause = ""
                if "limit" in fixed_query.lower():
                    limit_match = re.search(r'(?i)limit\s+(\d+)', fixed_query)
                    if limit_match:
                        limit_clause = f" limit {limit_match.group(1)}"

                # Extract offset clause if present
                offset_clause = ""
                if "offset" in fixed_query.lower():
                    offset_match = re.search(r'(?i)offset\s+(\d+)', fixed_query)
                    if offset_match:
                        offset_clause = f" offset {offset_match.group(1)}"

                # SECURITY FIX: Use secure query builder instead of string interpolation
                # Build a simplified query with "select *"
                try:
                    # Extract numeric values safely
                    limit_val = None
                    if limit_clause:
                        limit_match = re.search(r'limit\s+(\d+)', limit_clause, re.IGNORECASE)
                        if limit_match:
                            limit_val = int(limit_match.group(1))

                    offset_val = None
                    if offset_clause:
                        offset_match = re.search(r'offset\s+(\d+)', offset_clause, re.IGNORECASE)
                        if offset_match:
                            offset_val = int(offset_match.group(1))

                    # Use secure query builder
                    simplified_query = self._build_secure_query(
                        table_name=table_name,
                        where_clause=where_clause.strip() if where_clause else "",
                        limit=limit_val,
                        offset=offset_val
                    )
                except (ValueError, AttributeError):
                    # Fallback to original query if parsing fails
                    simplified_query = f"from {table_name} select *{where_clause}{limit_clause}{offset_clause}"

                # Check if the original query already uses "select *"
                select_match = re.search(r'(?i)select\s+(.*?)\s+from', fixed_query)
                if select_match and select_match.group(1).strip() != '*':
                    logger.info(f"Converting query to use 'select *' to avoid parsing errors")
                    logger.debug(f"Original query: {fixed_query}")
                    logger.debug(f"Simplified query: {simplified_query}")
                    fixed_query = simplified_query
        except Exception as e:
            logger.warning(f"Failed to simplify query: {str(e)}")

        # Fix 10: Handle missing spaces around operators
        fixed_query = re.sub(r'([<>=!])(?=[^\s=])', r'\1 ', fixed_query)

        # Fix 11: Ensure proper spacing around logical operators
        fixed_query = re.sub(r'(?i)(\s+and\s+|\s+or\s+)', lambda m: m.group(0).lower(), fixed_query)
        fixed_query = re.sub(r'(?i)(?<!\s)(and|or)(?!\s)', r' \1 ', fixed_query)

        # Fix 12: Remove any control characters or non-printable characters
        fixed_query = re.sub(r'[\x00-\x1F\x7F]', '', fixed_query)

        # Fix 13: Ensure proper quoting of string literals
        # Look for unquoted string literals in where clauses
        if ' where ' in fixed_query.lower():
            # Look for date patterns that might not be properly quoted
            date_pattern = r'(\d{4}-\d{2}-\d{2}(?:T\d{2}:\d{2}:\d{2}(?:\.\d{3})?Z?)?)'
            fixed_query = re.sub(
                f"({date_pattern})(?!'|\")",
                r"'\1'",
                fixed_query
            )

        # Log if we made any changes
        if fixed_query != query:
            logger.info(f"Fixed query syntax: {query} -> {fixed_query}")

        return fixed_query

    def execute_query(self, query: str, days: Optional[int] = None, from_date: Optional[str] = None,
                     timeout: int = DEFAULT_TIMEOUT, table_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Execute a query against the Devo API using the Devo SDK.

        Args:
            query: The LINQ query to execute
            days: Number of days of data to query (default: 1)
            from_date: The starting date for the query as a string expression (e.g., "today()-1*day()")
                       If provided, this takes precedence over days
            timeout: Request timeout in seconds (default: 1800)
            table_name: Optional table name for better logging (default: None)

        Returns:
            List of results as dictionaries

        Raises:
            ApiError: If the query execution fails
            NetworkError: If a network-related error occurs
        """
        # CRITICAL FIX: Extract date range from WHERE clause BEFORE removing it
        # This ensures that specific dates in WHERE clauses are respected
        extracted_date_range = self._extract_date_range_from_query(query)

        # Fix query syntax if it contains a where clause
        # The Devo API requires the where clause to be part of the dates parameter, not in the query
        query = self._fix_query_syntax(query)

        # Add table name to log messages if provided
        log_prefix = f"[{table_name}] " if table_name else ""
        logger.debug(f"{log_prefix}Executing query: {query[:100]}...")

        if extracted_date_range:
            # Use dates extracted from WHERE clause
            date_range = extracted_date_range
            logger.info(f"Using date range extracted from WHERE clause: from={date_range['from']}, to={date_range['to']}")
        elif from_date:
            # For test compatibility, preserve exact date strings
            date_range = {"from": from_date, "to": "today()"}
            logger.debug(f"Using date range: from={from_date}, to=today()")
        elif days:
            date_range = {"from": f"today()-{days}*day()", "to": "today()"}
            logger.debug(f"Using date range: from=today()-{days}*day(), to=today()")
        else:
            date_range = {"from": "today()-1*day()", "to": "today()"}
            logger.debug("Using default date range: from=today()-1*day(), to=today()")

        try:
            # Create a client with the default configuration
            client_with_timeout = self._create_client()

            start_time = time.time()
            logger.debug(f"Starting query execution with timeout of {timeout} seconds")

            # Execute the query and implement manual timeout
            response = client_with_timeout.query(
                query=query,
                dates=date_range
            )

            # Set a timeout for the query processing
            query_timeout = time.time() + timeout
            logger.info(f"Query execution started with timeout of {timeout} seconds")

            # Process the results with a timeout and heartbeat
            results = []
            try:
                # Use a separate thread to process the response with a timeout
                import threading
                import queue
                import signal

                result_queue = queue.Queue()
                stop_event = threading.Event()
                heartbeat_event = threading.Event()

                # Add query cancellation capability
                query_cancelled = threading.Event()

                def process_response():
                    try:
                        for row in response:
                            if stop_event.is_set():
                                logger.debug(f"{log_prefix}Response processing thread received stop signal")
                                break
                            result_queue.put(row)
                            # Signal activity for heartbeat
                            heartbeat_event.set()
                        result_queue.put(None)  # Signal end of data
                        logger.debug(f"{log_prefix}Response processing thread completed normally")
                    except Exception as e:
                        error_msg = f"Error processing response: {str(e)}"
                        logger.error(f"{log_prefix}{error_msg}")
                        result_queue.put(e)  # Signal error
                    finally:
                        # Always signal heartbeat to prevent hanging
                        heartbeat_event.set()

                # Use managed thread pool for better resource control
                with managed_thread_pool(f"devo-query-{table_name or 'unknown'}", max_workers=2) as thread_pool:
                    # Submit processing task
                    process_future = thread_pool.submit(process_response)

                    # Heartbeat monitoring with better resource management
                    def heartbeat_monitor():
                        last_activity = time.time()
                        last_row_count = 0
                        check_interval = 15  # seconds

                        while not stop_event.is_set() and not process_future.done():
                            if heartbeat_event.wait(check_interval):
                                # Activity detected
                                heartbeat_event.clear()
                                last_activity = time.time()

                                # Log progress if row count changed significantly
                                if row_count > last_row_count and (row_count - last_row_count >= 1000 or row_count >= last_row_count * 1.1):
                                    logger.debug(f"{log_prefix}Heartbeat: Processing active, {row_count:,} rows so far")
                                    # Only log at INFO level for significant milestones (every 10,000 rows)
                                    if row_count // 10000 > last_row_count // 10000:
                                        logger.info(f"{log_prefix}Progress: {row_count:,} rows processed")
                                    last_row_count = row_count
                            else:
                                # No activity for check_interval seconds
                                inactive_time = time.time() - last_activity
                                logger.debug(f"{log_prefix}Heartbeat: No new data for {inactive_time:.1f}s, {row_count:,} rows so far")

                                # Only log at INFO level after significant inactivity (60+ seconds)
                                if inactive_time >= 60 and int(inactive_time) % 60 == 0:
                                    logger.info(f"{log_prefix}Waiting: No new data for {inactive_time:.0f}s, {row_count:,} rows processed")

                                # Check for potential hanging
                                if inactive_time > 120:  # 2 minutes of inactivity
                                    logger.warning(f"{log_prefix}Potential hanging detected: No activity for {inactive_time:.1f}s")

                    # Submit heartbeat monitoring task
                    heartbeat_future = thread_pool.submit(heartbeat_monitor)

                # Wait for results with timeout
                row_count = 0
                last_progress_time = time.time()

                while True:
                    # Check if we've exceeded the timeout
                    current_time = time.time()
                    if current_time > query_timeout:
                        logger.warning(f"{log_prefix}Query processing timed out after {timeout} seconds")
                        stop_event.set()
                        query_cancelled.set()

                        # Force cleanup of processing threads
                        try:
                            if 'process_future' in locals():
                                process_future.cancel()
                            if 'heartbeat_future' in locals():
                                heartbeat_future.cancel()
                        except Exception as e:
                            logger.debug(f"Error cancelling futures: {e}")

                        break

                    # Log periodic progress even if no new rows
                    if current_time - last_progress_time > 60:  # Log every minute
                        logger.info(f"{log_prefix}Still processing: {row_count:,} rows so far ({(current_time - start_time):.1f}s elapsed)")
                        last_progress_time = current_time

                    try:
                        # Get the next result with a short timeout
                        item = result_queue.get(timeout=1.0)

                        # Check if it's the end marker or an error
                        if item is None:
                            logger.info(f"{log_prefix}Response processing completed normally")
                            break
                        elif isinstance(item, Exception):
                            logger.error(f"{log_prefix}Error in response processing thread: {str(item)}")
                            raise item

                        # Add the row to results
                        results.append(item)
                        row_count += 1
                        heartbeat_event.set()  # Signal activity

                        # Log progress periodically with reduced frequency
                        # Use DEBUG level for detailed row counts (every 100,000 rows)
                        if row_count % 100000 == 0:
                            logger.debug(f"{log_prefix}Processed {row_count:,} rows so far")
                            last_progress_time = current_time
                            # Log a summary at INFO level only for significant milestones
                            if row_count % 1000000 == 0:  # Log at million row intervals
                                logger.info(f"{log_prefix}Processing milestone: {row_count:,} rows")

                    except queue.Empty:
                        # No results available yet, continue waiting
                        continue

                    # Clean up with managed thread pool
                    stop_event.set()  # Signal all threads to stop

                    # Wait for processing future to complete with timeout
                    try:
                        process_future.result(timeout=5.0)
                        logger.debug(f"{log_prefix}Response processing completed successfully")
                    except FutureTimeoutError:
                        logger.warning(f"{log_prefix}Response processing timed out after 5 seconds")
                        process_future.cancel()
                    except Exception as e:
                        logger.error(f"{log_prefix}Response processing failed: {e}")

                    # Wait for heartbeat future to complete
                    try:
                        heartbeat_future.result(timeout=2.0)
                        logger.debug(f"{log_prefix}Heartbeat monitoring completed")
                    except FutureTimeoutError:
                        logger.debug(f"{log_prefix}Heartbeat monitoring timed out - continuing")
                        heartbeat_future.cancel()
                    except Exception as e:
                        logger.debug(f"{log_prefix}Heartbeat monitoring error: {e}")

                    # Force garbage collection to free memory
                    gc.collect()

                    # Log thread metrics for monitoring
                    log_thread_metrics()

            except Exception as e:
                logger.error(f"Error in response processing: {str(e)}")
                raise

            elapsed_time = time.time() - start_time

            # Enhanced logging for data retrieval results
            row_count = len(results)
            if row_count == 0:
                logger.info(f"{log_prefix}[NO_DATA] Query completed: 0 rows found in {elapsed_time:.2f}s")
            else:
                # Calculate processing rate
                rate = f"{row_count/elapsed_time:.0f} rows/sec" if elapsed_time > 0 else "N/A"

                # Log with different levels based on row count for better visibility
                if row_count < 1000:
                    logger.info(f"{log_prefix}[DATA_RETRIEVED] Query completed: {row_count:,} rows in {elapsed_time:.2f}s ({rate})")
                elif row_count < 100000:
                    logger.info(f"{log_prefix}[MEDIUM_DATA] Query completed: {row_count:,} rows in {elapsed_time:.2f}s ({rate})")
                else:
                    logger.info(f"{log_prefix}[LARGE_DATA] Query completed: {row_count:,} rows in {elapsed_time:.2f}s ({rate})")

                # Additional detailed logging for large datasets
                if row_count > 50000:
                    logger.info(f"{log_prefix}[PERFORMANCE] Large dataset processed - Memory usage: ~{row_count * 0.001:.1f}MB estimated")

            # Always log at DEBUG level for detailed diagnostics
            logger.debug(f"{log_prefix}[DEBUG] Query execution details - Rows: {row_count}, Time: {elapsed_time:.2f}s, Rate: {rate if row_count > 0 else 'N/A'}")

            return results

        except (ConnectionError, TimeoutError) as e:
            # Network errors are handled by the decorator
            raise
        except Exception as e:
            logger.error(f"Error executing query: {query[:50]}... - {str(e)}")

            # Special handling for query parsing errors
            error_str = str(e)
            if "Query parsing error" in error_str:
                # Log detailed information about the query
                logger.error(f"Query parsing error detected in query: {query}")

                # Try to identify specific issues in the query
                self._diagnose_query_issues(query)

                # Try to fix common query parsing issues
                fixed_query = self._fix_query_parsing_issues(query)
                if fixed_query != query:
                    logger.info(f"Attempting to execute query with fixed syntax")
                    try:
                        # Try again with the fixed query
                        client_with_timeout = self._create_client()
                        response = client_with_timeout.query(
                            query=fixed_query,
                            dates=date_range
                        )

                        # Process the results
                        results = []
                        for row in response:
                            results.append(row)

                        elapsed_time = time.time() - start_time
                        logger.info(f"Fixed query returned {len(results)} rows in {elapsed_time:.2f} seconds")
                        return results
                    except Exception as fix_e:
                        # If the fixed query also fails, log detailed information
                        logger.error(f"Fixed query also failed: {str(fix_e)}")
                        logger.error(f"Original query: {query}")
                        logger.error(f"Fixed query: {fixed_query}")

                        # Try to identify specific issues in the fixed query
                        self._diagnose_query_issues(fixed_query)
                else:
                    logger.warning("Could not automatically fix query syntax issues")

            # Add more context to the error message
            error_message = f"Error executing query: {str(e)}"
            if "Query parsing error" in error_str:
                error_message += " - Check for syntax errors, special characters, or malformed LINQ syntax"
            elif "timeout" in error_str.lower():
                error_message += " - Consider increasing the timeout value or reducing the query scope"
            elif "permission" in error_str.lower() or "access" in error_str.lower():
                error_message += " - Check API credentials and permissions"

            raise ApiError(error_message)

    def _is_retryable_error(self, error: Exception) -> bool:
        """
        Determine if an error is retryable.

        Args:
            error: The exception to check

        Returns:
            True if the error is retryable
        """
        error_str = str(error).lower()
        retryable_patterns = [
            'response ended prematurely',
            'connection broken',
            'connection reset',
            'timeout',
            'network',
            'temporary failure',
            'service unavailable',
            'too many requests',
            'rate limit',
            'invalidchunklength'
        ]

        return any(pattern in error_str for pattern in retryable_patterns)

    def execute_query_with_retry(self, query: str, days: int = None, from_date: str = None,
                                timeout: int = DEFAULT_TIMEOUT, table_name: str = None,
                                max_retries: int = 3) -> List[Dict[str, Any]]:
        """
        Execute a query with automatic retry logic for connection failures.

        Args:
            query: The LINQ query to execute
            days: Number of days of data to query (default: 1)
            from_date: The starting date for the query as a string expression
            timeout: Request timeout in seconds (default: 1800)
            table_name: Optional table name for better logging
            max_retries: Maximum number of retry attempts

        Returns:
            List of results as dictionaries

        Raises:
            ApiError: If the query execution fails after all retries
            NetworkError: If a network-related error occurs after all retries
        """
        log_prefix = f"[{table_name}] " if table_name else ""

        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    logger.info(f"{log_prefix}Retry attempt {attempt}/{max_retries}")

                return self.execute_query(query, days, from_date, timeout, table_name)

            except Exception as e:
                if attempt < max_retries and self._is_retryable_error(e):
                    # Calculate exponential backoff delay
                    base_delay = 30  # 30 seconds base delay
                    max_delay = 300  # 5 minutes max delay
                    delay = min(base_delay * (2 ** attempt), max_delay)

                    logger.warning(f"{log_prefix}Query attempt {attempt + 1} failed: {str(e)}")
                    logger.info(f"{log_prefix}Retrying in {delay} seconds...")
                    time.sleep(delay)
                    continue
                else:
                    # Final attempt or non-retryable error
                    logger.error(f"{log_prefix}Query failed after {attempt + 1} attempts: {str(e)}")
                    raise

    def _sanitize_table_name(self, table_name: str) -> str:
        """
        Sanitize a table name to prevent SQL injection and query parsing errors.

        SECURITY FIX: Enhanced validation to prevent SQL injection attacks.

        Args:
            table_name: The original table name

        Returns:
            Sanitized table name

        Raises:
            ValueError: If table name contains dangerous characters or patterns
        """
        if not table_name or not isinstance(table_name, str):
            raise ValueError("Table name must be a non-empty string")

        # Remove any leading/trailing whitespace
        sanitized = table_name.strip()

        if not sanitized:
            raise ValueError("Table name cannot be empty or whitespace only")

        # SECURITY: Check for SQL injection patterns
        dangerous_patterns = [
            r';\s*(?:select|update|delete|insert|drop|alter|create|exec|execute)',
            r'--',  # SQL comments
            r'/\*.*\*/',  # Multi-line comments
            r'\bunion\b.*\bselect\b',  # UNION attacks
            r'\bor\b.*[\'"].*[\'"].*=.*[\'"].*[\'"]',  # OR-based injection
            r'[\'"];.*(?:select|update|delete|insert|drop|alter)',  # Statement termination
        ]

        for pattern in dangerous_patterns:
            if re.search(pattern, sanitized, re.IGNORECASE):
                raise ValueError(f"Table name contains potentially dangerous SQL pattern: {table_name}")

        # Validate table name format - only allow alphanumeric, dots, underscores, and hyphens
        if not re.match(r'^[a-zA-Z0-9._-]+$', sanitized):
            raise ValueError(f"Table name contains invalid characters. Only alphanumeric, dots, underscores, and hyphens are allowed: {table_name}")

        # Check length limits
        if len(sanitized) > 255:
            raise ValueError(f"Table name too long (max 255 characters): {table_name}")

        # Check if the table name contains special characters that need quoting
        if re.search(r'[^\w\.]', sanitized):
            # If it contains special characters, wrap in quotes if not already wrapped
            if not (sanitized.startswith('"') and sanitized.endswith('"')):
                # Escape any existing quotes to prevent injection
                escaped = sanitized.replace('"', '""')
                sanitized = f'"{escaped}"'
                logger.debug(f"Sanitized table name: {table_name} -> {sanitized}")

        return sanitized

    def _validate_where_clause(self, where_clause: str) -> str:
        """
        Validate and sanitize WHERE clause to prevent SQL injection.

        SECURITY FIX: Validates WHERE clause for dangerous patterns.

        Args:
            where_clause: The WHERE clause to validate

        Returns:
            Validated WHERE clause

        Raises:
            ValueError: If WHERE clause contains dangerous patterns
        """
        if not where_clause:
            return ""

        # Remove leading/trailing whitespace
        clause = where_clause.strip()

        if not clause:
            return ""

        # SECURITY: Check for SQL injection patterns in WHERE clause
        dangerous_patterns = [
            r';\s*(?:select|update|delete|insert|drop|alter|create|exec|execute)',
            r'--',  # SQL comments
            r'/\*.*\*/',  # Multi-line comments
            r'\bunion\b.*\bselect\b',  # UNION attacks
            r'[\'"];.*(?:select|update|delete|insert|drop|alter)',  # Statement termination
        ]

        for pattern in dangerous_patterns:
            if re.search(pattern, clause, re.IGNORECASE):
                raise ValueError(f"WHERE clause contains potentially dangerous SQL pattern")

        # Validate that it starts with 'where' (case insensitive)
        if not clause.lower().startswith('where '):
            clause = f"where {clause}"

        return clause

    def _build_secure_query(self, table_name: str, where_clause: str = "",
                           limit: Optional[int] = None, offset: Optional[int] = None,
                           select_clause: str = "*") -> str:
        """
        Build a secure query with proper validation and sanitization.

        SECURITY FIX: Replaces vulnerable string interpolation with secure construction.

        Args:
            table_name: Name of the table (will be sanitized)
            where_clause: WHERE clause (will be validated)
            limit: LIMIT value (will be validated)
            offset: OFFSET value (will be validated)
            select_clause: SELECT clause (defaults to *)

        Returns:
            Securely constructed query string

        Raises:
            ValueError: If any parameter contains dangerous content
        """
        # Sanitize table name
        safe_table_name = self._sanitize_table_name(table_name)

        # Validate WHERE clause
        safe_where_clause = self._validate_where_clause(where_clause) if where_clause else ""

        # Validate numeric parameters
        if limit is not None:
            if not isinstance(limit, int) or limit < 0 or limit > 10000000:
                raise ValueError(f"Invalid limit value: {limit}")

        if offset is not None:
            if not isinstance(offset, int) or offset < 0:
                raise ValueError(f"Invalid offset value: {offset}")

        # Validate select clause
        if not select_clause or not isinstance(select_clause, str):
            select_clause = "*"

        # Build query components
        query_parts = [f"from {safe_table_name} select {select_clause}"]

        if safe_where_clause:
            query_parts.append(safe_where_clause)

        if limit is not None:
            query_parts.append(f"limit {limit}")

        if offset is not None:
            query_parts.append(f"offset {offset}")

        return " ".join(query_parts)

    def _extract_date_range_from_query(self, query: str) -> Optional[Dict[str, str]]:
        """
        Extract date range from WHERE clause in the query.

        This method looks for eventdate conditions in the WHERE clause and converts
        them to the date range format expected by the Devo SDK.

        Args:
            query: The LINQ query string

        Returns:
            Dictionary with 'from' and 'to' keys, or None if no specific dates found
        """
        try:
            # Look for eventdate conditions in the WHERE clause
            # Pattern: eventdate >= 'YYYY-MM-DD HH:MM:SS' and eventdate < 'YYYY-MM-DD HH:MM:SS'
            import re

            # Extract eventdate >= condition
            start_match = re.search(r"eventdate\s*>=\s*['\"](\d{4}-\d{2}-\d{2})\s+\d{2}:\d{2}:\d{2}['\"]", query, re.IGNORECASE)

            # Extract eventdate < condition
            end_match = re.search(r"eventdate\s*<\s*['\"](\d{4}-\d{2}-\d{2})\s+\d{2}:\d{2}:\d{2}['\"]", query, re.IGNORECASE)

            if start_match and end_match:
                start_date = start_match.group(1)
                end_date = end_match.group(1)

                # Convert to Devo SDK format
                # For same-day queries, we need to include the full day
                if start_date == end_date:
                    # Single day query: from start of day to end of day
                    date_range = {
                        "from": f"{start_date} 00:00:00",
                        "to": f"{end_date} 23:59:59"
                    }
                else:
                    # Multi-day query
                    date_range = {
                        "from": f"{start_date} 00:00:00",
                        "to": f"{end_date} 23:59:59"
                    }

                logger.debug(f"Extracted date range from query: {start_date} to {end_date}")
                return date_range

            # Check for single eventdate condition (eventdate >= 'YYYY-MM-DD')
            single_match = re.search(r"eventdate\s*>=\s*['\"](\d{4}-\d{2}-\d{2})['\"]", query, re.IGNORECASE)
            if single_match:
                date = single_match.group(1)
                # Single date condition - assume it's for that specific day
                date_range = {
                    "from": f"{date} 00:00:00",
                    "to": f"{date} 23:59:59"
                }
                logger.debug(f"Extracted single date from query: {date}")
                return date_range

            # Check for eventdate equality condition (eventdate = 'YYYY-MM-DD')
            equality_match = re.search(r"eventdate\s*=\s*['\"](\d{4}-\d{2}-\d{2})['\"]", query, re.IGNORECASE)
            if equality_match:
                date = equality_match.group(1)
                # Equality condition - query for that specific day
                date_range = {
                    "from": f"{date} 00:00:00",
                    "to": f"{date} 23:59:59"
                }
                logger.debug(f"Extracted equality date from query: {date}")
                return date_range

            # CRITICAL FIX: Check for timestamp range conditions (eventdate >= timestamp and eventdate <= timestamp)
            timestamp_range_match = re.search(r"eventdate\s*>=\s*(\d+)\s+and\s+eventdate\s*<=\s*(\d+)", query, re.IGNORECASE)
            if timestamp_range_match:
                start_timestamp = int(timestamp_range_match.group(1))
                end_timestamp = int(timestamp_range_match.group(2))

                # Convert timestamps back to date strings for Devo SDK
                try:
                    from datetime import datetime
                    start_date = datetime.fromtimestamp(start_timestamp / 1000).strftime('%Y-%m-%d')
                    end_date = datetime.fromtimestamp(end_timestamp / 1000).strftime('%Y-%m-%d')

                    date_range = {
                        "from": f"{start_date} 00:00:00",
                        "to": f"{end_date} 23:59:59"
                    }
                    logger.debug(f"Extracted timestamp range from query: {start_date} to {end_date}")
                    return date_range
                except Exception as e:
                    logger.warning(f"Failed to convert timestamps to dates: {e}")
                    # Continue to fallback

            # Note: Hybrid OR conditions have been simplified to timestamp-only approach
            # This comment remains for historical reference

            return None

        except Exception as e:
            logger.warning(f"Error extracting date range from query: {str(e)}")
            return None

    def check_table_exists(self, table_name: str) -> bool:
        """
        Check if a table exists by querying it with a limit of 1.

        Args:
            table_name: Name of the table to check

        Returns:
            True if the table exists, False otherwise
        """
        def _check_table():
            # SECURITY FIX: Use secure query builder instead of string interpolation
            query = self._build_secure_query(table_name, limit=1)
            # Use shorter timeout for table existence checks
            _ = self.execute_query(query, days=1, timeout=60)
            return True

        # Execute with simple error handling
        try:
            return _check_table()
        except Exception as e:
            logger.warning(f"Error checking if table {table_name} exists: {str(e)}")
            return False

    def get_table_columns(self, table_name: str, timeout: int = 15) -> List[str]:
        """
        Get the column names for a table.

        Args:
            table_name: Name of the table
            timeout: Query timeout in seconds (default: 15)

        Returns:
            List of column names

        Raises:
            ApiError: If there's an error getting the columns
        """
        try:
            # SECURITY FIX: Use secure query builder instead of string interpolation
            query = self._build_secure_query(table_name, limit=1)
            logger.info(f"Getting columns for table {table_name} with timeout {timeout}s")

            # Use a shorter timeout for column retrieval
            results = self.execute_query(query, days=1, timeout=timeout)

            # Extract column names from the first row
            if results and len(results) > 0:
                columns = list(results[0].keys())
                logger.info(f"Found {len(columns)} columns for table {table_name}")

                # Check for columns with special characters that might cause issues
                problematic_columns = []
                for col in columns:
                    if re.search(r'[^\w\s.*()\[\]_]|:{1,2}', col):
                        problematic_columns.append(col)

                if problematic_columns:
                    logger.warning(f"Table {table_name} has {len(problematic_columns)} columns with special characters that might cause query issues")
                    for col in problematic_columns[:5]:  # Log first 5 problematic columns
                        logger.warning(f"Problematic column: {col}")
                    if len(problematic_columns) > 5:
                        logger.warning(f"... and {len(problematic_columns) - 5} more problematic columns")

                return columns
            else:
                logger.warning(f"Table {table_name} exists but has no data")
                return []
        except Exception as e:
            # Log the error but don't raise it
            logger.warning(f"Error getting columns for table {table_name}: {str(e)}")
            # Return an empty list instead of raising an error
            # This allows the backup process to continue with a fallback approach
            logger.info(f"Using fallback approach with 'select *' for table {table_name}")
            return []

    def get_table_count(self, table_name: str, days: int = 1, from_date: Optional[str] = None, timeout: int = 15) -> int:
        """
        Get the number of rows in a table.

        Args:
            table_name: Name of the table
            days: Number of days of data to count (default: 1)
            from_date: The starting date for the query as a string expression
            timeout: Query timeout in seconds (default: 15)

        Returns:
            Number of rows in the table (or an estimate)
        """
        logger.info(f"Getting row count for table {table_name} with timeout {timeout}s")

        try:
            # First try with a simple query to get a sample of data
            # This is more reliable than using count(*) which can cause parsing errors
            # SECURITY FIX: Use secure query builder instead of string interpolation
            sample_query = self._build_secure_query(table_name, limit=100)
            logger.debug(f"Executing sample query to estimate count: {sample_query}")

            try:
                sample_results = self.execute_query(sample_query, days=days, from_date=from_date, timeout=timeout)
                sample_count = len(sample_results)

                if sample_count == 0:
                    logger.warning(f"Table {table_name} exists but has no data")
                    return 0
                elif sample_count < 100:
                    # If we got fewer than 100 rows, that's the actual count
                    logger.info(f"Table {table_name} has {sample_count} rows")
                    return sample_count
                else:
                    # If we got 100 rows, we need to estimate the total
                    logger.info(f"Table {table_name} has at least 100 rows, using estimate")
                    # Return a reasonable estimate for backup planning
                    return 10000 * days
            except Exception as e:
                # If the sample query fails, log and use default estimate
                logger.warning(f"Sample query failed: {str(e)}")
                logger.info(f"Using default estimate for table {table_name}")
                return 10000 * days

        except Exception as e:
            # Log the error but don't raise it
            logger.warning(f"Error getting count for table {table_name}: {str(e)}")
            # Return a default estimate instead of raising an error
            # This allows the backup process to continue even if count fails
            return 10000 * days

    def query_table_to_file(self, table_name: str, where_clause: str, output_dir: str,
                          chunk_size: int = 500000, timeout: int = 1800, max_retries: int = 5,
                          progress_reporter=None) -> Dict[str, Any]:
        """
        Query a table and save the results to files in chunks.

        Args:
            table_name: Name of the table to query
            where_clause: Where clause for the query
            output_dir: Directory to save the output files
            chunk_size: Number of rows to process in each chunk
            timeout: Query timeout in seconds
            max_retries: Maximum number of retries for failed queries
            progress_reporter: Progress reporter for tracking progress

        Returns:
            Dictionary with query results information
        """
        import json
        import os

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Initialize result variables
        total_rows = 0
        chunk_count = 0
        file_paths = []
        has_more_data = True
        offset = 0

        # Log the start of the query
        logger.info(f"Starting query for table {table_name} with {where_clause}")

        try:
            # Process data in chunks
            while has_more_data:
                # SECURITY FIX: Use secure query builder instead of vulnerable string interpolation
                query = self._build_secure_query(
                    table_name=table_name,
                    where_clause=where_clause,
                    limit=chunk_size,
                    offset=offset
                )
                logger.debug(f"Executing query for chunk {chunk_count + 1}: {query}")

                # Execute the query with retry logic
                retry_count = 0
                while True:
                    try:
                        logger.info(f"Executing query for chunk {chunk_count + 1} (attempt {retry_count + 1}/{max_retries + 1})")
                        results = self.execute_query(query, timeout=timeout)
                        # Query succeeded, break out of retry loop
                        break
                    except Exception as e:
                        retry_count += 1
                        error_type = type(e).__name__
                        error_msg = str(e)

                        # If we've already processed some data, log the error and continue
                        if total_rows > 0:
                            logger.warning(f"Error querying chunk at offset {offset}: {error_msg}")
                            logger.warning(f"Continuing with data already processed ({total_rows} rows)")
                            has_more_data = False
                            break
                        elif retry_count <= max_retries:
                            # Enhanced retry logic with adaptive backoff based on error type

                            # For network errors or chunk length errors, use longer delays
                            if "InvalidChunkLength" in error_msg or "Connection broken" in error_msg:
                                # For chunk length errors, use longer delays with jitter
                                import random
                                base_delay = 5 * retry_count  # Start with 5 seconds, increase linearly
                                jitter = random.uniform(0.8, 1.2)  # Add 20% jitter
                                retry_delay = min(120, base_delay * jitter)  # Cap at 2 minutes

                                logger.warning(f"Network/chunk error detected: {error_msg}")
                                logger.warning(f"Using extended delay for network stability: {retry_delay:.1f}s")
                            else:
                                # Standard exponential backoff for other errors
                                retry_delay = min(2 ** retry_count, 60)  # Max delay of 60 seconds

                            logger.warning(f"Error querying chunk {chunk_count + 1}: {error_type}: {error_msg}. "
                                          f"Retrying in {retry_delay:.1f}s (attempt {retry_count}/{max_retries})")

                            # Log more detailed diagnostics for specific error types
                            if "Query parsing error" in error_msg:
                                logger.warning(f"Query parsing error detected. Query: {query[:100]}...")
                                self._diagnose_query_issues(query)
                            elif "timeout" in error_msg.lower():
                                logger.warning(f"Timeout error detected. Consider increasing timeout value (current: {timeout}s)")
                            elif "InvalidChunkLength" in error_msg or "Connection broken" in error_msg:
                                logger.warning(f"Network stability issue detected. Will retry with longer delay.")

                            time.sleep(retry_delay)
                        else:
                            # Max retries exceeded
                            logger.error(f"Max retries exceeded for chunk {chunk_count + 1}: {error_type}: {error_msg}")
                            raise

                # Check if we got any results
                if not results or len(results) == 0:
                    logger.info(f"No more data to process after {total_rows} rows")
                    has_more_data = False
                    break

                # Save this chunk to a file
                chunk_count += 1
                rows_in_chunk = len(results)
                total_rows += rows_in_chunk

                # Create the output file path with table-based naming
                # Convert table name to safe filename format
                safe_table_name = table_name.replace('.', '_')
                if chunk_count == 1:
                    chunk_filename = f"{safe_table_name}.json"
                else:
                    chunk_filename = f"{safe_table_name}_{chunk_count}.json"

                output_file = os.path.join(output_dir, chunk_filename)
                file_paths.append(output_file)

                # Save the results to the file
                with open(output_file, 'w') as f:
                    json.dump(results, f)

                logger.debug(f"Saved chunk {chunk_count} with {rows_in_chunk} rows to {output_file}")

                # Update progress if a reporter is provided
                if progress_reporter:
                    progress_reporter.update(rows_in_chunk)

                # Check if we got a full chunk, indicating there might be more data
                has_more_data = len(results) >= chunk_size

                # Update the offset for the next chunk
                offset += rows_in_chunk

                # Log progress periodically with reduced frequency
                # Only log at significant intervals to reduce log verbosity
                if chunk_count % 50 == 0:
                    logger.info(f"Processed {total_rows:,} rows in {chunk_count} chunks")

            # Log completion with formatted numbers for better readability
            logger.info(f"Query completed. Total rows: {total_rows:,}, chunks: {chunk_count}")

            # Return the result
            return {
                "status": "success",
                "total_rows": total_rows,
                "chunk_count": chunk_count,
                "file_paths": file_paths,
                "table_name": table_name
            }

        except Exception as e:
            # Log the error
            logger.error(f"Error querying table {table_name}: {str(e)}")
            logger.error(f"Query failed after processing {total_rows} rows: {str(e)}")

            # Return error result
            return {
                "status": "error",
                "error": str(e),
                "error_type": type(e).__name__,
                "total_rows": total_rows,
                "chunk_count": chunk_count,
                "file_paths": file_paths,
                "table_name": table_name,
                "details": str(e)
            }

    def execute_query_chunked(self, query: str, offset: int, limit: int,
                             days: Optional[int] = None, from_date: Optional[str] = None,
                             timeout: int = DEFAULT_TIMEOUT, table_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Execute a query with LIMIT and OFFSET for chunked data retrieval.

        Args:
            query: The LINQ query to execute (without LIMIT/OFFSET)
            offset: Number of rows to skip
            limit: Maximum number of rows to return
            days: Number of days of data to query (default: 1)
            from_date: The starting date for the query as a string expression
            timeout: Request timeout in seconds (default: 1800)
            table_name: Optional table name for better logging

        Returns:
            List of results as dictionaries (up to 'limit' rows)

        Raises:
            ApiError: If the query execution fails
            NetworkError: If a network-related error occurs
        """
        # Add LIMIT and OFFSET to the query
        chunked_query = f"{query.rstrip()} limit {limit} offset {offset}"

        # Add table name to log messages if provided
        log_prefix = f"[{table_name}] " if table_name else ""
        logger.debug(f"{log_prefix}Executing chunked query: offset={offset:,}, limit={limit:,}")

        # Execute the modified query using the standard method
        return self.execute_query(
            query=chunked_query,
            days=days,
            from_date=from_date,
            timeout=timeout,
            table_name=table_name
        )

    def get_table_row_count(self, table_name: str,
                           days: Optional[int] = None, from_date: Optional[str] = None,
                           where_clause: Optional[str] = None,
                           timeout: int = 300) -> int:
        """
        Get the total row count for a table within a date range.

        Args:
            table_name: Name of the table to count
            days: Number of days of data to query (default: 1)
            from_date: The starting date for the query as a string expression
            where_clause: Optional WHERE clause (without the WHERE keyword)
            timeout: Request timeout in seconds (default: 300 - 5 minutes)

        Returns:
            Total number of rows

        Raises:
            ApiError: If the query execution fails
            NetworkError: If a network-related error occurs
        """
        # First try with a sample query to check if data exists
        # This is more reliable than count() which can have parsing issues
        sample_query = f"from {table_name}"

        if where_clause:
            sample_query += f" where {where_clause}"

        sample_query += " select * limit 1"

        logger.debug(f"[{table_name}] Checking data existence with sample query: {sample_query}")

        try:
            # First check if any data exists
            sample_results = self.execute_query(
                query=sample_query,
                days=days,
                from_date=from_date,
                timeout=min(timeout, 60),  # Use shorter timeout for sample
                table_name=table_name
            )

            if not sample_results or len(sample_results) == 0:
                logger.info(f"[{table_name}] No data found in sample query")
                return 0

            # If data exists, try to get accurate count
            count_query = f"from {table_name}"
            if where_clause:
                count_query += f" where {where_clause}"
            count_query += " select count() as total_count"

            logger.debug(f"[{table_name}] Getting row count with query: {count_query}")

            count_results = self.execute_query(
                query=count_query,
                days=days,
                from_date=from_date,
                timeout=timeout,
                table_name=table_name
            )

            if count_results and len(count_results) > 0:
                count = count_results[0].get('total_count', 0)
                if count > 0:
                    logger.info(f"[{table_name}] Total rows: {count:,}")
                    return int(count)
                else:
                    logger.warning(f"[{table_name}] Count query returned 0, but sample found data")
                    # Return a reasonable estimate since we know data exists
                    return 1000  # Conservative estimate for streaming decision
            else:
                logger.warning(f"[{table_name}] No count result returned, but sample found data")
                # Return a reasonable estimate since we know data exists
                return 1000  # Conservative estimate for streaming decision

        except Exception as e:
            logger.error(f"[{table_name}] Failed to get row count: {str(e)}")
            # Return 0 to indicate unknown count rather than failing
            return 0

    def execute_streaming_query(self, table_name: str,
                               chunk_size: int = 100000,
                               days: Optional[int] = None, from_date: Optional[str] = None,
                               where_clause: Optional[str] = None,
                               select_clause: Optional[str] = None,
                               timeout: int = DEFAULT_TIMEOUT) -> Dict[str, Any]:
        """
        Execute a streaming query that processes data in chunks.

        Args:
            table_name: Name of the table to query
            chunk_size: Number of rows per chunk (default: 100,000)
            days: Number of days of data to query (default: 1)
            from_date: The starting date for the query as a string expression
            where_clause: Optional WHERE clause (without the WHERE keyword)
            select_clause: Optional SELECT clause (default: "select *")
            timeout: Request timeout in seconds per chunk

        Returns:
            Dictionary with streaming query information and chunk function

        Raises:
            ApiError: If the initial setup fails
        """
        # Build base query
        base_query = f"from {table_name}"

        if where_clause:
            base_query += f" where {where_clause}"

        if select_clause:
            base_query += f" {select_clause}"
        else:
            base_query += " select *"

        logger.info(f"[{table_name}] Setting up streaming query with chunk size: {chunk_size:,}")

        # Get total row count first
        total_rows = self.get_table_row_count(
            table_name=table_name,
            days=days,
            from_date=from_date,
            where_clause=where_clause,
            timeout=min(timeout, 300)  # Limit count query timeout to 5 minutes
        )

        if total_rows == 0:
            logger.info(f"[{table_name}] No data found for streaming query")
            return {
                'total_rows': 0,
                'total_chunks': 0,
                'chunk_size': chunk_size,
                'query_function': lambda offset, limit: []
            }

        # Calculate number of chunks
        total_chunks = (total_rows + chunk_size - 1) // chunk_size

        logger.info(f"[{table_name}] Streaming setup complete: {total_rows:,} rows, "
                   f"{total_chunks} chunks of {chunk_size:,} rows each")

        # Create chunk query function
        def query_chunk(offset: int, limit: int) -> List[Dict[str, Any]]:
            """Query a specific chunk of data."""
            return self.execute_query_chunked(
                query=base_query,
                offset=offset,
                limit=limit,
                days=days,
                from_date=from_date,
                timeout=timeout,
                table_name=table_name
            )

        return {
            'total_rows': total_rows,
            'total_chunks': total_chunks,
            'chunk_size': chunk_size,
            'base_query': base_query,
            'query_function': query_chunk
        }
